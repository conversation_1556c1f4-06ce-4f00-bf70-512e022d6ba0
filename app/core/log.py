import logging
import sys
from logging.handlers import RotatingFileHandler
import os
from typing import Optional
from app.core.config import settings
from colorlog import ColoredFormatter

class AppLogger:
    """应用日志管理类"""
    
    _instance: Optional['AppLogger'] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._initialized = True
        self.log_dir = str(settings.LOG_PATH)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 配置根日志记录器
        self.root_logger = logging.getLogger()
        self.root_logger.setLevel(logging.DEBUG if settings.DEBUG else logging.INFO)
        
        # 文件日志使用普通格式
        self.file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d - %(funcName)s()] - %(message)s'
        )
        
        # 控制台日志使用彩色格式
        self.color_formatter = ColoredFormatter(
            "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d - %(funcName)s()] - %(message)s%(reset)s",
            log_colors={
                "DEBUG": "cyan",
                "INFO": "green",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "bold_red",
            },
            reset=True,
            secondary_log_colors={},
            style='%'
        )
        
        # 添加处理器
        self._setup_handlers()
        
        # 设置第三方库的日志级别
        self._setup_third_party_loggers()
    
    def _setup_handlers(self):
        """配置日志处理器"""
        # 控制台处理器（使用彩色格式）
        console = logging.StreamHandler(sys.stdout)
        console.setFormatter(self.color_formatter)
        self.root_logger.addHandler(console)
        
        # 文件处理器
        log_file = os.path.join(self.log_dir, 'app.log')
        error_log_file = os.path.join(self.log_dir, 'error.log')
        
        # 常规日志文件处理器
        file_handler = RotatingFileHandler(
            filename=log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.file_formatter)
        file_handler.setLevel(logging.INFO)
        self.root_logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_file_handler = RotatingFileHandler(
            filename=error_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        error_file_handler.setFormatter(self.file_formatter)
        error_file_handler.setLevel(logging.ERROR)
        self.root_logger.addHandler(error_file_handler)
    
    def _setup_third_party_loggers(self):
        """配置第三方库的日志级别"""
        third_party_loggers = [
            "urllib3",
            "playwright",
            "asyncio",
        ]
        for logger_name in third_party_loggers:
            logging.getLogger(logger_name).setLevel(logging.WARNING)
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的logger实例"""
        return logging.getLogger(name)

# 全局日志管理器实例
app_logger = AppLogger()

def get_logger(name: str) -> logging.Logger:
    """获取logger的便捷方法"""
    return app_logger.get_logger(name)

def setup_logging():
    """初始化日志系统"""
    global app_logger
    app_logger = AppLogger()
