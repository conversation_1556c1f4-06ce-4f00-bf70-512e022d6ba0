from pydantic_settings import BaseSettings
from pathlib import Path
import os

class Settings(BaseSettings):
    PROJECT_NAME: str
    VERSION: str
    API_V1_STR: str
    DEBUG: bool

    # DeepSeek API 配置
    DS_API_URL: str = "https://api.deepseek.com"
    DS_API_KEY: str 


    # 新榜 API 配置
    XINRED_API_URL: str = "https://gw.newrank.cn"
    XINRED_N_TOKEN: str
    XINRED_API_TIMEOUT: int = 30
    XINRED_MAX_RETRIES: int = 3
    XINRED_RATE_LIMIT: int = 30
    XINRED_RATE_WINDOW: int = 60
    
    # 新榜账号配置
    XINRED_USERNAME: str
    XINRED_PASSWORD: str
    
    # Cookie 文件路径配置
    BASE_DIR: Path = Path(__file__).parent.parent.parent  # 项目根目录
    CORE_DIR: Path = Path(__file__).parent               # 直接使用当前目录
    XINRED_COOKIE_FILE: Path = CORE_DIR / "xh_cookies.json"
    XINRED_LAST_LOGIN_FILE: Path = CORE_DIR / "last_login.txt"
    XHS_COOKIE_FILE: Path = CORE_DIR.parent / "core" / "cookies_file"
    
    # 飞书配置
    FEISHU_APP_ID: str = ""
    FEISHU_APP_SECRET: str = ""
    FEISHU_BASE_URL: str = "https://open.feishu.cn/open-apis"
    # 多维表
    FEISHU_PASET_APP_TOKEN: str = ""
    # 新红关键词数据-穿搭打扮
    FEISHU_XH_TABLE_ID: str = ""
    # 新红关键词数据-穿搭打扮-存放历史数据
    FEISHU_XH_HISTORY_TABLE_ID: str = ""
    # 小红书笔记数据-（本品当日）-笔记发布入口
    FEISHU_XHS_TABLE_ID: str = ""
    # 小红书笔记数据-（本品）-存放历史数据
    FEISHU_XHS_HISTORY_TABLE_ID: str = ""
    # 小红书笔记记数据-竞品当日数据
    FEISHU_CP_TABLE_ID: str = ""
    # 小红书笔记记数据-竞品存放历史数据
    FEISHU_CP_HISTORY_TABLE_ID: str = ""
    # 竞品配置
    FEISHU_CP_CONFIG: str = ""
    # 小红书配置
    FEISHU_XHS_CONFIG: str = ""
    # 小红书笔记数据-笔记生产和发布
    FEISHU_PRODUCT_PUBLISH: str = ""

    # 日志配置
    LOG_PATH: Path = BASE_DIR / "logs"

    # 临时图片保存路径
    TMP_IMAGES_PATH: Path = BASE_DIR / "app/lark/tmp_images/lark_images"
    QR_CODE_PATH: Path = BASE_DIR / "app/lark/tmp_images/qr_code"

    # 小红书JS
    XHS_JS_PATH: Path = BASE_DIR / "app/xhs/utils/stealth.min.js"
    class Config:
        env_file = Path(__file__).parent.parent.parent / ".env"
        env_file_encoding = 'utf-8'
        extra = "allow"

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

settings = Settings() 