#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import time
import functools
from typing import Type, Tuple, Optional, Callable
from requests.exceptions import SSLError, ConnectionError
from urllib3.exceptions import NewConnectionError
from app.core.log import get_logger

logger = get_logger("Retry")

def retry_on_network_error(
    max_retries: int = 3,
    retry_delay: int = 2,
    error_types: Tuple[Type[Exception], ...] = (
        ConnectionError,
        TimeoutError,
        SSLError,
        NewConnectionError,
    ),
    should_retry: Optional[Callable[[Exception], bool]] = None
):
    """
    网络请求重试装饰器
    
    :param max_retries: 最大重试次数
    :param retry_delay: 重试延迟（秒）
    :param error_types: 需要重试的错误类型
    :param should_retry: 自定义判断是否需要重试的函数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            retry_count = 0
            last_exception = None

            while retry_count < max_retries:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    retry_count += 1

                    # 检查是否是需要重试的错误类型
                    is_retryable = isinstance(e, error_types) or (
                        any(isinstance(e, t) for t in error_types)
                    )

                    # 检查错误信息中是否包含特定字符串
                    error_msg = str(e).lower()
                    network_errors = [
                        'network is unreachable',
                        'connection refused',
                        'connection reset',
                        'connection aborted',
                        'ssl error',
                        'eof occurred',
                        'max retries exceeded',
                        'timeout',
                        'connection error'
                    ]
                    is_network_error = any(err in error_msg for err in network_errors)

                    # 如果提供了自定义判断函数，则使用它
                    if should_retry:
                        should_retry_result = should_retry(e)
                    else:
                        should_retry_result = True

                    if (is_retryable or is_network_error) and should_retry_result and retry_count < max_retries:
                        logger.warning(
                            f"执行 {func.__name__} 时发生错误: {str(e)}, "
                            f"{retry_delay}秒后进行第{retry_count}次重试"
                        )
                        time.sleep(retry_delay)
                        continue
                    else:
                        logger.error(
                            f"执行 {func.__name__} 失败(重试{retry_count}次后): {str(e)}"
                        )
                        raise last_exception

            # 如果所有重试都失败了
            raise last_exception

        return wrapper
    return decorator 