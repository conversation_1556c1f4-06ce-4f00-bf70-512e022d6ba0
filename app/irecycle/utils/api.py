import sys
from pathlib import Path
# Add project root to Python path
sys.path.append(str(Path(__file__).resolve().parent.parent.parent.parent))
import os
import requests
from app.core.config import settings
from openai import OpenAI
import json

class LLMClient:
    """大模型基础 API客户端"""

    def __init__(self):
        self.base_url = settings.DS_API_URL
        self.api_key = settings.DS_API_KEY
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)

    def _dsrequest(self, prompt: str, user_info: str):
        response = self.client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {"role": "system", "content": prompt},
                {"role": "user", "content": user_info},
            ],
            max_tokens=1024,
            temperature=0.7,
            stream=False
        )
        return response.choices[0].message.content

    def complete(self,prompt:str = "", user_info: str = ""):
        url = "https://api.deepseek.com/chat/completions"
        payload = json.dumps({
            "messages": [
                {
                    "content": prompt,
                    "role": "system"
                },
                {
                    "content": user_info,
                    "role": "user"
                }
            ],
            "model": "deepseek-chat",
            "frequency_penalty": 0,
            "max_tokens": 2048,
            "presence_penalty": 0,
            "response_format": {
                "type": "text"
            },
            "stop": None,
            "stream": False,
            "stream_options": None,
            "temperature": 1,
            "top_p": 1,
            "tools": None,
            "tool_choice": "none",
            "logprobs": False,
            "top_logprobs": None
        })
        headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': 'Bearer '+ self.api_key
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        print(response.json()["choices"][0]["message"]["content"] if response.status_code == 200 else "Error: " + str(
            response.status_code))

if __name__ == '__main__':
    client = LLMClient()
    prompt = "你是一名意图分析师，你需要结合我给你的内容去做意图分析和需求拆解"
    user_info = """
    **公司名称/产品链接**：公司名称：四川极蜜科技有限公司；产品链接：[京东店铺链接]
    - **网站**：没有提供公司网站。产品在京东上，所以可以说“京东店铺”或“无”。我会写“京东店铺链接： [链接]”
    - **告知你的产品是什么**：极蜜高定 天然成熟蜂蜜豪华礼盒，包含四种成熟蜂蜜：红花蜜（新疆塔城，促进血液循环、抗氧化）、枇杷蜜（四川龙泉，润肺止咳、增强免疫力）、五倍子蜜（四川平武，抗菌消炎、调节肠胃）、龙眼蜜（四川泸州合江，改善睡眠、健脾养胃）。
    - **谁是你的目标群体**：高端银发养生客群、养生美体女性群体、往来赠礼商务客群。
    - **你曾经定位过哪些用户？**：需求中没有提及历史定位，因此写“未指定”或“无信息”。
    - **希望找哪些用户？**：这应与目标群体一致，因为它是当前营销重点。所以，与目标群体相同。
    - **想要给客户带来什么价值**：
  - 对于商务赠礼：体现高品质调性、营养和药用价值、情绪关怀。
  - 对于自用：高品质产品、良好功效、真实性、视觉、味觉、功效的良好体验。
"""
    response = client.complete(prompt, user_info)
    print(response)