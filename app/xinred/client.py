import logging
from typing import Dict, Any, Optional
import uuid
import time
from datetime import datetime, date  # 添加 datetime 导入
from tenacity import retry, stop_after_attempt, wait_exponential
from app.core.config import settings
from app.xinred.utils.api import APIClient
from app.xinred.utils.errors import XinRedAPIError, XinRedErrorResponse
from app.xinred.utils.rate_limiter import RateLimiter
from app.core.log import get_logger
from app.core.retry import retry_on_network_error

logger = get_logger("XinRedClient")

class XinRedClient(APIClient):
    """新红 API 客户端"""

    def __init__(self):
        super().__init__()
        self.rate_limiter = RateLimiter()
        self._session = None

    def __enter__(self):
        """同步上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """同步上下文管理器出口"""
        if self._session:
            self._session.close()
        self._session = None

    def _handle_response(self, response: Dict) -> Dict:
        """统一处理 API 响应"""
        try:
            # 处理错误响应
            error = XinRedErrorResponse(**response)
            if error.is_error:
                if error.code == 4001:  # 未登录错误
                    self.auth.invalidate_cookies()
                    return None  # 返回 None 表示需要重试
                raise XinRedAPIError(
                    code=error.code,
                    message=str(error),
                    data=error.data
                )

            # 处理数据为空的情况
            if not response.get("data"):
                return {
                    "code": response.get("code", 0),
                    "msg": response.get("msg", "No data"),
                    "data": self._get_default_data(response.get("endpoint"))
                }

            return response

        except Exception as e:
            logger.error(f"Error handling response: {e}")
            return {
                "code": -1,
                "msg": str(e),
                "data": self._get_default_data(response.get("endpoint"))
            }

    def _get_default_data(self, endpoint: str) -> Dict:
        """根据不同的接口返回对应的默认数据结构"""
        defaults = {
            # 热词榜单默认值
            "/api/xhsv2/nr/app/xh/v2/rank/hotWordHotList": {
                "list": [],
                "total": 0,
                "count": 0,
                "resultCount": "0",
                "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            },

            # 热词详情默认值
            "/api/xhsv2/nr/app/xh/v2/hotWord/detail": {
                "noteCount": 0,
                "hot": 0,
                "likeCount": 0,
                "collectCount": 0,
                "commentCount": 0,
                "noteChainRatio": 0,
                "hotChainRatio": 0,
                "likeChainRatio": 0,
                "collectChainRatio": 0,
                "commentChainRatio": 0
            },

            # 热词天数默认值
            "/api/xhsv2/nr/app/xh/v2/hotWord/day": {
                "hotCount": 0,
                "dayCount": 0,
                "isEvergreen": False
            },

            # 受众画像默认值
            "/api/xh/xdnphb/nr/app/xhs/hotWord/audiencePortrait/overview": {
                "wordFansAll": 0,
                "provinceTop": [],
                "genderTop": [],
                "ageTop": [],
                "interestsTop": [],
                "commentCount": 0,
                "positiveCountPercent": 0,
                "neutralCountPercent": 0,
                "negativeCountPercent": 0
            }
        }

        # 如果找不到对应的默认值，返回空对象
        return defaults.get(endpoint, {})

    @retry(
        stop=stop_after_attempt(settings.XINRED_MAX_RETRIES),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送 HTTP 请求并处理重试"""
        start_time = time.time()
        request_id = str(uuid.uuid4())

        # 记录请求信息
        logger.info(
            "API Request Started",
            extra={
                "request_id": request_id,
                "method": method,
                "url": f"{self.base_url}{endpoint}",
                "endpoint": endpoint,
                "params": kwargs.get("params"),
                "data": kwargs.get("json") or kwargs.get("data"),
                "headers": kwargs.get("headers")
            }
        )

        try:
            # 应用速率限制
            self.rate_limiter.acquire()

            # 发送请求
            response = super()._make_request(method, endpoint, **kwargs)

            # 检查是否需要重新登录
            if response.get("code") == 4001:  # 未登录错误
                logger.info("Token expired, refreshing cookies...")
                # 清除旧的 cookies 并重新登录
                self.auth.invalidate_cookies()
                self.auth.get_valid_cookies()
                # 使用新的 cookies 重新发送请求
                response = super()._make_request(method, endpoint, **kwargs)

                # 如果还是失败，则抛出异常
                if response.get("code") == 4001:
                    raise XinRedAPIError(code=4001, message="Login failed after retry")
            
            # 记录成功请求
            duration = time.time() - start_time
            logger.info(
                "API Request Successful",
                extra={
                    "request_id": request_id,
                    "method": method,
                    "url": f"{self.base_url}{endpoint}",
                    "duration": f"{duration:.3f}s",
                    "status": response.get("code"),
                    "response": response
                }
            )
            
            return response
            
        except Exception as e:
            # 记录失败请求
            duration = time.time() - start_time
            logger.error(
                "API Request Failed",
                extra={
                    "request_id": request_id,
                    "method": method,
                    "url": f"{self.base_url}{endpoint}",
                    "duration": f"{duration:.3f}s",
                    "error": str(e),
                    "error_type": type(e).__name__,
                    "params": kwargs.get("params"),
                    "data": kwargs.get("json") or kwargs.get("data")
                }
            )
            raise

    @retry_on_network_error(max_retries=1, retry_delay=2)
    def get_hot_words(
        self,
        rank_date: Optional[date] = None,
        rank_type: str = "day",
        type_v1: str = "",
        type_v2: str = "",
        size: int = 20,
        start: int = 1,
        is_new: str = "",
        is_boom: str = "",
        sort: str = "hot_score"
    ) -> Dict:
        """获取热词榜单"""
        endpoint = "/api/xhsv2/nr/app/xh/v2/rank/hotWordHotList"
        
        # 如果没有指定日期，使用昨天的日期
        if rank_date is None:
            rank_date = date.today()
        
        data = {
            "typeV1": type_v1,
            "typeV2": type_v2,
            "rankType": rank_type,
            "rankDate": rank_date.strftime("%Y-%m-%d"),
            "recentType": "",
            "size": str(size),  # 转换为字符串
            "start": str(start),  # 转换为字符串
            "isNew": is_new,
            "isBoom": is_boom,
            "sort": sort,
            "request_id": str(uuid.uuid4())  # 添加请求ID
        }
        
        # 移除空值参数
        data = {k: v for k, v in data.items() if v}
        
        return self._make_request("POST", endpoint, json=data)  # 使用 json 参数而不是 data

    @retry_on_network_error(max_retries=1, retry_delay=2)
    def get_hot_word_detail(
        self,
        keyword: str,
        start_time: str = "",
        end_time: str = "",
        time: str = "7d",
        search_type: int = 1
    ) -> Dict:
        """获取热词详情"""
        endpoint = "/api/xhsv2/nr/app/xh/v2/hotWord/detail"
        
        data = {
            "startTime": start_time,
            "endTime": end_time,
            "time": time,
            "searchType": str(search_type),  # 转换为字符串
            "keyword": keyword,
            "request_id": str(uuid.uuid4())  # 添加请求ID
        }
        
        # 移除空值参数
        data = {k: v for k, v in data.items() if v}
        
        return self._make_request("POST", endpoint, json=data)

    @retry_on_network_error(max_retries=1, retry_delay=2)
    def get_hot_word_day(
        self,
        keyword: str,
        start_time: str = "",
        end_time: str = "",
        time: str = "7d",
        search_type: int = 1,
        is_business: int = 0
    ) -> Dict:
        """获取热词每日数据"""
        endpoint = "/api/xhsv2/nr/app/xh/v2/hotWord/hotDay"
        
        data = {
            "searchType": str(search_type),  # 转换为字符串
            "time": time,
            "startTime": start_time,
            "endTime": end_time,
            "isBusiness": str(is_business),  # 转换为字符串
            "keyword": keyword,
            "request_id": str(uuid.uuid4())  # 添加请求ID
        }
        
        # 移除空值参数
        data = {k: v for k, v in data.items() if v}
        
        return self._make_request("POST", endpoint, json=data)

    @retry_on_network_error(max_retries=1, retry_delay=2)
    def get_audience_portrait(
        self,
        keyword: str,
        start_time: str = "",
        end_time: str = "",
        time: str = "7d"
    ) -> Dict:
        """获取热词受众画像概览"""
        endpoint = "/api/xh/xdnphb/nr/app/xhs/hotWord/audiencePortrait/overview"
        
        data = {
            "time": time,
            "startTime": start_time,
            "endTime": end_time,
            "keyword": keyword,
            "request_id": str(uuid.uuid4())  # 添加请求ID
        }
        
        # 移除空值参数
        data = {k: v for k, v in data.items() if v}
        
        return self._make_request("POST", endpoint, json=data)

    @retry_on_network_error(max_retries=1, retry_delay=2)
    def get_note_analysis_list(
        self,
        rid: str,
        sort: str = "createTime",
        type: str = "",
        bistype: str = "",
        feature_type: str = "",
        is_hide_delete: str = "0",
        is_cooperate: str = "",
        title: str = "",
        size: int = 30,
        start: int = 1,
        time: str = "30d",
        start_time: str = "",
        end_time: str = ""
    ) -> Dict:
        """获取笔记分析列表
        
        Args:
            rid: 用户ID
            sort: 排序方式，默认按创建时间
            type: 笔记类型
            bistype: 商业类型
            feature_type: 特征类型
            is_hide_delete: 是否隐藏删除，0-不隐藏，1-隐藏
            is_cooperate: 是否合作
            title: 标题关键词
            size: 每页数量
            start: 起始页码
            time: 时间范围，如 "30d"
            start_time: 开始时间
            end_time: 结束时间
        """
        endpoint = "/api/xh/xdnphb/nr/app/xhs/red/user/detail/noteAna/list"
        
        data = {
            "rid": rid,
            "sort": sort,
            "type": type,
            "bistype": bistype,
            "featureType": feature_type,
            "isHideDelete": is_hide_delete,
            "isCooperate": is_cooperate,
            "title": title,
            "size": str(size),
            "start": str(start),
            "time": time,
            "startTime": start_time,
            "endTime": end_time,
            "request_id": str(uuid.uuid4())
        }
        
        # 移除空值参数
        data = {k: v for k, v in data.items() if v}
        
        response = self._make_request("POST", endpoint, json=data)
        response["endpoint"] = endpoint
        return response


