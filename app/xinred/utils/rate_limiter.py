import time
import logging
from threading import Lock
from app.core.config import settings
from app.core.log import app_logger
from app.xinred.utils.errors import RateLimitExceeded

# 初始化 logger
logger = app_logger.get_logger("Rate Limiter")

class RateLimiter:
    """API 速率限制器

    使用令牌桶算法实现 API 请求速率限制
    - rate_limit: 每个时间窗口的最大请求数
    - rate_window: 时间窗口大小（秒）
    """

    def __init__(self):
        self.rate_limit = settings.XINRED_RATE_LIMIT  # 每个时间窗口的最大请求数
        self.rate_window = settings.XINRED_RATE_WINDOW  # 时间窗口大小（秒）
        self.tokens = self.rate_limit  # 可用令牌数
        self.last_update = time.time()  # 上次更新时间
        self.lock = Lock()  # 用于线程安全的锁

        logger.info(f"RateLimiter initialized: rate_limit={self.rate_limit}, rate_window={self.rate_window}s")

    def acquire(self):
        """获取一个令牌，如果没有可用令牌则等待"""
        with self.lock:
            self._update_tokens()

            if self.tokens <= 0:
                # 计算需要等待的时间
                wait_time = self.rate_window - (time.time() - self.last_update)
                logger.warning(f"No tokens available. Waiting for {wait_time:.2f} seconds.")

                if wait_time > 0:
                    time.sleep(wait_time)
                    self._update_tokens()

                # 如果还是没有令牌，抛出异常
                if self.tokens <= 0:
                    logger.error("Rate limit exceeded. Raising RateLimitExceeded exception.")
                    raise RateLimitExceeded()

            self.tokens -= 1
            logger.info(f"Token acquired. Remaining tokens: {self.tokens:.2f}")

    def _update_tokens(self):
        """更新可用令牌数"""
        now = time.time()
        time_passed = now - self.last_update

        # 如果已经过了一个时间窗口
        if time_passed >= self.rate_window:
            self.tokens = self.rate_limit
            self.last_update = now
            logger.info(f"Token bucket refilled. Tokens reset to {self.rate_limit}.")
        else:
            # 按比例恢复令牌
            new_tokens = (time_passed / self.rate_window) * self.rate_limit
            old_tokens = self.tokens
            self.tokens = min(self.rate_limit, self.tokens + new_tokens)
            self.last_update = now
            logger.debug(f"Tokens updated: {old_tokens:.2f} -> {self.tokens:.2f}")

    def get_remaining_tokens(self) -> float:
        """获取剩余的令牌数"""
        remaining = self.tokens
        logger.info(f"Remaining tokens: {remaining:.2f}")
        return remaining

    def get_reset_time(self) -> float:
        """获取下次重置时间（秒）"""
        reset_time = max(0, self.rate_window - (time.time() - self.last_update))
        logger.info(f"Time until next reset: {reset_time:.2f} seconds")
        return reset_time