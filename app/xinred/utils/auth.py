from playwright.sync_api import sync_playwright
import json
import time
from datetime import datetime, timedelta
import random
from app.core.config import settings
from app.core.log import get_logger

logger = get_logger("xinred.auth")

class XinRedAuth:
    def __init__(self, username: str, password: str):
        settings.CORE_DIR.mkdir(parents=True, exist_ok=True)  # 确保目录存在
        
        self.cookies_file = settings.XINRED_COOKIE_FILE
        self.last_login_file = settings.XINRED_LAST_LOGIN_FILE
        
        self.username = username
        self.password = password
    
    def login(self) -> dict:
        """自动登录并获取 cookies"""
        with sync_playwright() as p:
            # 添加更真实的浏览器配置
            browser = p.chromium.launch(
                headless=True,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-features=IsolateOrigins,site-per-process',
                ]
            )
            
            # 创建上下文时添加更多真实特征
            context = browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                java_script_enabled=True,
                has_touch=True,
                locale='zh-CN',
                timezone_id='Asia/Shanghai',
            )
            
            # 注入脚本以绕过webdriver检测
            context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined
                });
            """)
            
            page = context.new_page()
            
            try:
                # 访问主页
                page.goto("https://xh.newrank.cn/")
                
                # 点击登录注册按钮 (使用更精确的选择器)
                page.click('button:has(span:text("登录/注册"))')
                
                # 等待弹窗出现
                page.wait_for_selector('div[role="dialog"]')
                
                # 点击其他方式登录
                page.click('span:text("· 其他登录方式")')
                
                # 等待输入框出现并输入账号密码
                page.fill('input[type="text"]', self.username)
                page.fill('input[type="password"]', self.password)
                
                # 点击登录按钮
                page.click('button:has-text("登 录")')
                
                # 等待滑块出现
                slider = page.wait_for_selector('#nc_1_n1z')
                slider_box = slider.bounding_box()
                
                # 滑动验证时添加更多随机性
                page.mouse.move(slider_box['x'], slider_box['y'] + slider_box['height']/2)
                time.sleep(random.uniform(0.1, 0.3))  # 随机等待
                page.mouse.down()
                time.sleep(random.uniform(0.1, 0.2))  # 随机等待
                
                steps = random.randint(25, 35)  # 随机步数
                distance = random.uniform(280, 320)  # 随机距离
                
                for i in range(steps):
                    progress = i / steps
                    # 使用更自然的缓动函数
                    eased_progress = progress * (2 - progress)  # 二次方缓动
                    current_distance = distance * eased_progress
                    
                    # 添加微小的随机偏移
                    offset_y = random.uniform(-1, 1)
                    
                    page.mouse.move(
                        slider_box['x'] + current_distance,
                        slider_box['y'] + slider_box['height']/2 + offset_y,
                        steps=random.randint(1, 3)  # 随机步数
                    )
                    time.sleep(random.uniform(0.008, 0.015))  # 随机延迟
                
                time.sleep(random.uniform(0.1, 0.2))  # 随机等待
                page.mouse.up()
                
                # 等待可能的刷新或重试
                page.wait_for_timeout(5000)
                
                # 检查是否出现错误提示
                error_text = page.text_content('.error-text') if page.query_selector('.error-text') else None
                if error_text and "出错了" in error_text:
                    # 如果出错，等待一会儿后重试
                    page.wait_for_timeout(2000)
                    # 这里可以添加重试逻辑
                    
                # 获取 cookies
                cookies = context.cookies()
                cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
                
                # 保存 cookies
                self.save_cookies(cookie_dict)
                self.save_login_time()
                
                logger.info("Login successful")
                return cookie_dict
                
            except Exception as e:
                logger.error(f"Login failed: {str(e)}")
                page.screenshot(path="login_error.png")
                raise
            
            finally:
                browser.close()
    
    def save_cookies(self, cookies: dict):
        """保存 cookies 到文件"""
        self.cookies_file.write_text(json.dumps(cookies))
    
    def save_login_time(self):
        """保存登录时间"""
        self.last_login_file.write_text(datetime.now().isoformat())
    
    def load_cookies(self) -> dict:
        """从文件加载 cookies"""
        if self.cookies_file.exists():
            return json.loads(self.cookies_file.read_text())
        return {}
    
    def is_cookies_expired(self) -> bool:
        """检查 cookies 是否过期"""
        if not self.last_login_file.exists():
            return True
            
        last_login = datetime.fromisoformat(self.last_login_file.read_text())
        return datetime.now() - last_login > timedelta(hours=1)  # 1小时后过期
    
    def get_valid_cookies(self) -> dict:
        """获取有效的 cookies"""
        cookies = self.load_cookies()
        if not cookies or self.is_cookies_expired():
            logger.info("Cookies expired or not found, performing new login")
            cookies = self.login()
        return cookies 
    
    def invalidate_cookies(self):
        """使当前 cookies 失效，强制重新登录"""
        if self.cookies_file.exists():
            self.cookies_file.unlink()  # 删除 cookies 文件
        if self.last_login_file.exists():
            self.last_login_file.unlink()  # 删除登录时间文件 