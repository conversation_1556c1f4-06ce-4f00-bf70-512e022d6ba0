from typing import Any, Optional
from pydantic import BaseModel

class XinRedErrorResponse(BaseModel):
    """新榜 API 错误响应模型"""
    code: int = 0
    msg: str = ""
    data: Optional[Any] = None
    
    @property
    def is_error(self) -> bool:
        """检查是否为错误响应
        code 不等于 0 表示有错误
        """
        return self.code != 0
    
    def __str__(self) -> str:
        return f"API Error: {self.msg} (code: {self.code})"

class XinRedAPIError(Exception):
    """新榜 API 错误"""
    def __init__(self, code: int, message: str, data: Any = None):
        self.code = code
        self.message = message
        self.data = data
        super().__init__(f"API Error: {message} (code: {code})")

class RateLimitExceeded(Exception):
    """请求频率超限异常"""
    def __init__(self, wait_seconds: int = 30):
        self.wait_seconds = wait_seconds
        super().__init__(f"Rate limit exceeded. Please wait {wait_seconds} seconds.") 