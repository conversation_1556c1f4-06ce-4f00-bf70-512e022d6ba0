from typing import Dict, Any, Optional
import requests
from app.core.config import settings
from app.xinred.utils.auth import XinRedAuth

class APIClient:
    """基础 API 客户端"""
    
    def __init__(self):
        self.base_url = settings.XINRED_API_URL
        self.timeout = settings.XINRED_API_TIMEOUT
        self._session: Optional[requests.Session] = None
        self.auth = XinRedAuth(settings.XINRED_USERNAME, settings.XINRED_PASSWORD)
    
    def _ensure_session(self):
        """确保 session 存在"""
        if not self._session:
            self._session = requests.Session()
    
    def _get_headers(self, additional_headers: Dict = None) -> Dict:
        """获取请求头"""
        # 获取最新的 cookies
        cookies = self.auth.get_valid_cookies()
        
        headers = {
            "Accept": "*/*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Connection": "keep-alive",
            "Cookie": "; ".join(f"{k}={v}" for k, v in cookies.items()),
            "Origin": "https://xh.newrank.cn",
            "Referer": "https://xh.newrank.cn/",
            "Sec-Fetch-Dest": "empty",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Site": "same-site",
            "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            "content-type": "application/json",
            "n-token": settings.XINRED_N_TOKEN,
            "sec-ch-ua": '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": '"macOS"'
        }
        if additional_headers:
            headers.update(additional_headers)
        return headers
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """发送 HTTP 请求"""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers(kwargs.pop('headers', None))
        
        self._ensure_session()
        response = self._session.request(
            method=method,
            url=url,
            headers=headers,
            timeout=self.timeout,
            **kwargs
        )
        return response.json() 